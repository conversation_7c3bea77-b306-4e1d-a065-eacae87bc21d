{"$message_type":"diagnostic","message":"unused import: `serde::Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":4,"byte_end":20,"line_start":1,"line_end":1,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use serde::Serialize;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":0,"byte_end":23,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::Serialize;","highlight_start":1,"highlight_end":22},{"text":"use std::path::Path;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `serde::Serialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::Serialize;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tauri::Emitter`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":49,"byte_end":63,"line_start":3,"line_end":3,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use tauri::Emitter;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":45,"byte_end":66,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tauri::Emitter;","highlight_start":1,"highlight_end":20},{"text":"use tauri_plugin_opener::OpenerExt;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tauri::Emitter`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tauri::Emitter;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\player\\mpv.rs","byte_start":233,"byte_end":244,"line_start":8,"line_end":8,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\player\\mpv.rs","byte_start":229,"byte_end":246,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":1,"highlight_end":17},{"text":"use thiserror::Error;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::thread`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\player\\mpv.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::thread;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `winapi::shared::windef::HWND`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\player\\mpv.rs","byte_start":272,"byte_end":300,"line_start":10,"line_end":10,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"use winapi::shared::windef::HWND;","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\player\\mpv.rs","byte_start":268,"byte_end":302,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use winapi::shared::windef::HWND;","highlight_start":1,"highlight_end":34},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `winapi::shared::windef::HWND`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\player\\mpv.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse winapi::shared::windef::HWND;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `RECT`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\player\\window_manager.rs","byte_start":35,"byte_end":39,"line_start":1,"line_end":1,"column_start":36,"column_end":40,"is_primary":true,"text":[{"text":"use winapi::shared::windef::{HWND, RECT};","highlight_start":36,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\player\\window_manager.rs","byte_start":33,"byte_end":39,"line_start":1,"line_end":1,"column_start":34,"column_end":40,"is_primary":true,"text":[{"text":"use winapi::shared::windef::{HWND, RECT};","highlight_start":34,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\player\\window_manager.rs","byte_start":28,"byte_end":29,"line_start":1,"line_end":1,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"use winapi::shared::windef::{HWND, RECT};","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\player\\window_manager.rs","byte_start":39,"byte_end":40,"line_start":1,"line_end":1,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use winapi::shared::windef::{HWND, RECT};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `RECT`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\player\\window_manager.rs:1:36\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse winapi::shared::windef::{HWND, RECT};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `GetWindowRect`, `HWND_TOPMOST`, `HWND_TOP`, and `SWP_SHOWWINDOW`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\player\\window_manager.rs","byte_start":73,"byte_end":86,"line_start":3,"line_end":3,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\player\\window_manager.rs","byte_start":125,"byte_end":133,"line_start":3,"line_end":3,"column_start":57,"column_end":65,"is_primary":true,"text":[{"text":"    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,","highlight_start":57,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\player\\window_manager.rs","byte_start":135,"byte_end":147,"line_start":3,"line_end":3,"column_start":67,"column_end":79,"is_primary":true,"text":[{"text":"    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,","highlight_start":67,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\player\\window_manager.rs","byte_start":167,"byte_end":181,"line_start":4,"line_end":4,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    SWP_SHOWWINDOW, SW_SHOW,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\player\\window_manager.rs","byte_start":73,"byte_end":88,"line_start":3,"line_end":3,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\player\\window_manager.rs","byte_start":123,"byte_end":147,"line_start":3,"line_end":3,"column_start":55,"column_end":79,"is_primary":true,"text":[{"text":"    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,","highlight_start":55,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\player\\window_manager.rs","byte_start":161,"byte_end":181,"line_start":3,"line_end":4,"column_start":93,"column_end":19,"is_primary":true,"text":[{"text":"    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,","highlight_start":93,"highlight_end":94},{"text":"    SWP_SHOWWINDOW, SW_SHOW,","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `GetWindowRect`, `HWND_TOPMOST`, `HWND_TOP`, and `SWP_SHOWWINDOW`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\player\\window_manager.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    GetWindowRect, SetParent, SetWindowPos, ShowWindow, HWND_TOP, HWND_TOPMOST, SWP_NOZORDER,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SWP_SHOWWINDOW, SW_SHOW,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tauri::Manager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\player\\window_manager.rs","byte_start":1607,"byte_end":1621,"line_start":63,"line_end":63,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    use tauri::Manager;","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\player\\window_manager.rs","byte_start":1603,"byte_end":1622,"line_start":63,"line_end":63,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    use tauri::Manager;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tauri::Manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\player\\window_manager.rs:63:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    use tauri::Manager;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0283","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0283\nlet x = \"hello\".chars().rev().collect();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nA common example is the `collect` method on `Iterator`. It has a generic type\nparameter with a `FromIterator` bound, which for a `char` iterator is\nimplemented by `Vec` and `String` among others. Consider the following snippet\nthat reverses the characters of a string:\n\nIn the first code example, the compiler cannot infer what the type of `x` should\nbe: `Vec<char>` and `String` are both suitable candidates. To specify which type\nto use, you can use a type annotation on `x`:\n\n```\nlet x: Vec<char> = \"hello\".chars().rev().collect();\n```\n\nIt is not necessary to annotate the full type. Once the ambiguity is resolved,\nthe compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nWe can see a self-contained example below:\n\n```compile_fail,E0283\nstruct Foo;\n\nimpl Into<u32> for Foo {\n    fn into(self) -> u32 { 1 }\n}\n\nlet foo = Foo;\nlet bar: u32 = foo.into() * 1u32;\n```\n\nThis error can be solved by adding type annotations that provide the missing\ninformation to the compiler. In this case, the solution is to specify the\ntrait's type parameter:\n\n```\nstruct Foo;\n\nimpl Into<u32> for Foo {\n    fn into(self) -> u32 { 1 }\n}\n\nlet foo = Foo;\nlet bar: u32 = Into::<u32>::into(foo) * 1u32;\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":5511,"byte_end":5515,"line_start":158,"line_end":158,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .open_path(&path, None)","highlight_start":31,"highlight_end":35}],"label":"cannot infer type of the type parameter `T` declared on the enum `Option`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":5494,"byte_end":5503,"line_start":158,"line_end":158,"column_start":14,"column_end":23,"is_primary":false,"text":[{"text":"            .open_path(&path, None)","highlight_start":14,"highlight_end":23}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"cannot satisfy `_: Into<std::string::String>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `Opener::<R>::open_path`","code":null,"level":"note","spans":[{"file_name":"D:\\scoop\\persist\\rustup\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-plugin-opener-2.3.0\\src\\lib.rs","byte_start":3107,"byte_end":3116,"line_start":108,"line_end":108,"column_start":12,"column_end":21,"is_primary":false,"text":[{"text":"    pub fn open_path(","highlight_start":12,"highlight_end":21}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"D:\\scoop\\persist\\rustup\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-plugin-opener-2.3.0\\src\\lib.rs","byte_start":3192,"byte_end":3204,"line_start":111,"line_end":111,"column_start":27,"column_end":39,"is_primary":true,"text":[{"text":"        with: Option<impl Into<String>>,","highlight_start":27,"highlight_end":39}],"label":"required by this bound in `Opener::<R>::open_path`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"consider specifying the generic argument","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":5515,"byte_end":5515,"line_start":158,"line_end":158,"column_start":35,"column_end":35,"is_primary":true,"text":[{"text":"            .open_path(&path, None)","highlight_start":35,"highlight_end":35}],"label":null,"suggested_replacement":"::<T>","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0283]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:158:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .open_path(&path, None)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot infer type of the type parameter `T` declared on the enum `Option`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: cannot satisfy `_: Into<std::string::String>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `Opener::<R>::open_path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mD:\\scoop\\persist\\rustup\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tauri-plugin-opener-2.3.0\\src\\lib.rs:111:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn open_path(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        with: Option<impl Into<String>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Opener::<R>::open_path`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider specifying the generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            .open_path(&path, None\u001b[0m\u001b[0m\u001b[38;5;10m::<T>\u001b[0m\u001b[0m)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::os::windows::process::CommandExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\mod.rs","byte_start":7397,"byte_end":7434,"line_start":226,"line_end":226,"column_start":13,"column_end":50,"is_primary":true,"text":[{"text":"        use std::os::windows::process::CommandExt;","highlight_start":13,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::os::windows::process::CommandExt`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\mod.rs:226:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        use std::os::windows::process::CommandExt;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 8 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 8 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0283`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0283`.\u001b[0m\n"}
